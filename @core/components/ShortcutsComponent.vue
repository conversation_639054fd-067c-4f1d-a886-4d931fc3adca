<template>
  <PerfectScrollbar :options="{ wheelPropagation: false }">
    <VRow class="ma-0 mt-n1">

        <VCol

            v-for="(shortcut, index) in shortcuts.filter(s=>$can(s.action, s.subject))"
            :key="shortcut.title"
            :cols="cols"
            class="text-center  cursor-pointer pa-6 shortcut-icon"
            @click="navigateTo(shortcut)"
        >
          <div v-if="$can(shortcut.action, shortcut.subject)">
            <VAvatar
                rounded
                :color="shortcut.color|| 'primary'"
                _variant="tonal"
                size="70"
            >
              <VIcon
                  color="white"

                  size="37"
                  :icon="shortcut.icon"
              />
            </VAvatar>

            <h6 class="text-h6 mt-3">
              {{ t(shortcut.title) }}
            </h6>
            <p class="text-sm text-medium-emphasis mb-0">
              {{ t(shortcut.subtitle) }}
            </p>
          </div>
        </VCol>

    </VRow>
  </PerfectScrollbar>
</template>
<script setup>
import {useI18n} from 'vue-i18n'
import {useTranslateUtil} from '@/utils/translate-util'
const router = useRouter()

const { t } = useI18n()
const { tc } = useTranslateUtil()
import shortcuts from '@/navigation/shortcuts.js'

const props = defineProps({
  cols: {
    type: Number,
    default: 6
  }
})

const navigateTo = (shortcut) => {
  // Se è un link esterno, apri in una nuova scheda
  if (shortcut.external && typeof shortcut.to === 'string') {
    window.open(shortcut.to, '_blank')
  } else {
    // Altrimenti usa il router interno
    router.push(shortcut.to)
  }
}

</script>
<style lang="scss">
.shortcut-icon:hover {
  background-color: rgba(var(--v-theme-on-surface), var(--v-hover-opacity));
}
</style>

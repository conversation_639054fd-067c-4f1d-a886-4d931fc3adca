<script setup>
import ShortcutsComponent from "@core/components/ShortcutsComponent.vue";
import {useI18n} from 'vue-i18n'
import {useTranslateUtil} from '@/utils/translate-util'


const { t } = useI18n()
const { tc } = useTranslateUtil()
const props = defineProps({
  togglerIcon: {
    type: String,
    required: false,
    default: 'ri-star-smile-line',
  },
  shortcuts: {
    type: Array,
    required: true,
  },
})

const router = useRouter()
</script>

<template>
  <IconBtn>
    <VIcon :icon="props.togglerIcon" />

    <VMenu
      activator="parent"
      offset="15px"
      location="bottom end"
    >
      <VCard
        width="500"
        max-height="560"
        class="d-flex flex-column"
      >
        <VCardItem class="px-4 py-2">
          <h5 class="text-h5">
            {{t('Scorciatoie')}}
          </h5>


        </VCardItem>

        <VDivider />

        <ShortcutsComponent/>
      </VCard>
    </VMenu>
  </IconBtn>
</template>


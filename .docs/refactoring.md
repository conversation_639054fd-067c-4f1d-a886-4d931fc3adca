# Piano di Refactoring QuoProfit

Questo documento elenca le modifiche richieste per il progetto QuoProfit e i file specifici da modificare, sia nelle pagine che negli store Pinia.

## 0. Gestione del versioning con GitFlow
**Operazione iniziale:**
- Verificare se esiste già la feature branch `feature/aggiornamento-giugno`
  - Se non esiste, creare una nuova feature branch utilizzando GitFlow: `feature/aggiornamento-giugno`
  - Se esiste già, non creare nuovi branch
- Fare checkout sul branch `feature/aggiornamento-giugno` come branch di lavoro
- Tutte le modifiche elencate di seguito andranno implementate in questa branch

## 1. Pagina di Ingresso
**Modifiche richieste:**
- Tradurre tutto in italiano
- Eliminare le icone social
- Rimuovere la scritta "New on our platform"

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/login.vue (da verificare)

## 2. Home
**Modifiche richieste:**
- <PERSON><PERSON><PERSON> bottone "Pubblica Prezzi"
- Agg<PERSON><PERSON><PERSON> bottone "Prenota Assistenza" collegato al link https://calendly.com/quoprofit-helpdesk

**File da modificare:**
- (Da identificare - potrebbe essere in /pages/index.vue o un altro file di layout principale)
- Richiede analisi ulteriore

## 3. Help
**Modifiche richieste:**
- Verificare richieste specifiche (non dettagliate)

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/help/guida.vue
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/help/requisiti.vue

## 4. Focus
**Modifiche richieste:**
- Aggiungere bottone "Importa Prenotazioni (API)"
- Modificare il testo in "Scegli file Prezzi Concorrenza ed Importali in Focus"

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/simulatore/view/edit.vue
- /Users/<USER>/IdeaProjects/revd-fe/components/focus/FiltroFocus.vue (da verificare)
- Store associati:
  - /Users/<USER>/IdeaProjects/revd-fe/stores/useSimulatore.js
  - /Users/<USER>/IdeaProjects/revd-fe/stores/useFilterFocusStore.js
  - /Users/<USER>/IdeaProjects/revd-fe/stores/usePrenotazioniDownloadStore.js

## 5. Tickets
**Modifiche richieste:**
- Aggiungere bottone "Prenota Assistenza" collegato al link Calendly
- Aggiungere messaggio iniziale come richiesto
- Implementare invio automatico di email al helpdesk su nuova comunicazione/ticket

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/tickets/view/edit.vue

## 6. Concorrenza
**Modifiche richieste:**
- Verificare richieste specifiche (non dettagliate)

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/concorrenza/view/edit.vue (da verificare)

## 7. Report
**Modifiche richieste:**
- Modifiche logiche calcolo ricavi/costi/profitto
- Nascondere/mostrare sezioni RMC-ADR come richiesto
- Aggiungere gestione contratti nei report

**File da modificare:**
- Files relativi ai report (da identificare)
- Store associati:
  - /Users/<USER>/IdeaProjects/revd-fe/stores/useCostiStore.js
  - /Users/<USER>/IdeaProjects/revd-fe/stores/useSimulatore.js
  - Altri store correlati ai calcoli (da verificare)

## 8. Strategia
**Modifiche richieste:**
- Modificare la fonte di calcolo
- Aggiungere opzione "Trattamenti gratis per Ospiti Letti Aggiuntivi (si/no)"
- Implementare recupero ricavi anno precedente dall'anagrafica

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/strategie_cliente/view/edit.vue (da verificare)
- /Users/<USER>/IdeaProjects/revd-fe/stores/useStrategieClienteStore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useFonteCalcoloStore.js

## 9. Impostazioni
**Modifiche richieste:**
- Rinominare varie etichette relative a integrazioni e trattamenti
- Modificare l'etichetta "Gestione Eventi" in "Gestione Eventi e Costi Extra"
- Eliminare il peso degli Eventi
- Inserire etichette da tendina in italiano nella sezione Tariffe
- Eliminare "Trattamenti per UA"

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/impostazioni/view/edit.vue (da verificare)
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/impostazioni_simulatore/view/edit.vue
- /Users/<USER>/IdeaProjects/revd-fe/stores/useImpostazioniSimulatore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useEventiStore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useTrattamentoAttivareStore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useMappaSigleuaStore.js

## 10. Contratti
**Modifiche richieste:**
- Implementare nuova sezione per gestione contratti
- Aggiungere valore totale per mese nei report
- Configurare calcolo del valore giornaliero come valore medio
- Implementare schema simile a Eventi ma senza pesi

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/contratti/ (nuovi file o modificare esistenti)
- /Users/<USER>/IdeaProjects/revd-fe/stores/ (creare nuovo store o modificare useEventiStore.js)

## 11. Attivazione
**Modifiche richieste:**
- Rinominare "Anagrafica" in "Registrazione - Firma"
- Implementare sistema a 5 sezioni con controllo scorrimento e checkbox
- Aggiungere menu a tendina per scelta Channel Manager
- Collegare le pagine di Contratto e Privacy QuoProfit
- Implementare il logging dei consensi

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/attivazione/ (file da verificare)
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/attivazione/funzionalita_quoprofit.vue
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/attivazione/listino_quoprofit.vue
- Store associati (da identificare)

## 12. Setup/Pannello di controllo
**Modifiche richieste:**
- Aggiungere sezioni Lingua e Tipo Software
- Implementare Data Entry delle tabelle
- Aggiungere esportazione Tickets con filtri
- Implementare pagina Funzionalità e Guida
- Configurare assegnazione permessi e attivazione nuova struttura
- Modificare generazione codice struttura da sequenziale a casuale

**File da modificare:**
- Pannello di controllo o file di setup (da identificare)

## 13. Logiche di calcolo Focus
**Modifiche richieste:**
- Rivedere il calcolo costi (automatico e manuale)
- Implementare determinazione pesi su date
- Aggiornare calcolo costi eventi
- Implementare calcolo Prezzo MUP e Prezzo Revenue
- Aggiornare logica e sequenzialità determinazione Prezzo

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/stores/useSimulatore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useCostiStore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useEventiStore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useModalitaCalcoloStore.js
- /Users/<USER>/IdeaProjects/revd-fe/stores/useVariazionePrezziOccupazioneStore.js

## 14. Eliminazioni
**Modifiche richieste:**
- Rimuovere sezione "Prenotazioni"
- Eliminare "Trattamenti per UA"

**File da modificare:**
- /Users/<USER>/IdeaProjects/revd-fe/pages/revd/admin/clienti/trattamento_x_ua/ (directory da rimuovere o modificare)
- File relativi a prenotazioni (da identificare)

## 15. Simulatore - Analisi Implementazione Funzionalità
**Analisi dello stato implementativo delle funzionalità descritte in CALCOLI SIMULATORE.md**

### 15.1 Funzionalità Implementate 

**Calcoli principali (presenti in useSimulatore.js):**
- **Calcolo Costo per sigla UA e data** - Implementato in `determinaCostoFocus()` e `calcolaCosto()`
  - Formula: `(CTSUA*PCUAD)/(numero massimo sigla UA)`
  - Gestione automatica e manuale
  - Calcolo peso su tipologia UA automatico

- **Calcolo Prezzo MUP** - Implementato in `calcolaPrezzoMup()`
  - Considera MOL Reale, MOL Auspicato, MOL Libero
  - Formula: `Costo per sigla su data*(MOL)`

- **Calcolo Prezzo Revenue** - Implementato in `calculatePrezzoRevenue()`
  - Usa tabella variazione prezzo in funzione dell'occupazione
  - Integrato con `useVariazionePrezziOccupazioneStore`

- **Gestione Eventi** - Implementato per "Eventi Unici"
  - Calcolo costo eventi per data e sigla UA
  - Distribuzione proporzionale sui giorni dell'evento

- **Prenotazioni e Occupazione** - Implementato
  - Lettura da IndexedDB
  - Calcolo percentuale occupazione per sigla e data

### 15.2 Funzionalità da Implementare/Verificare 

**Priorità Alta:**
- **Punto 1: UA Virtuale derivata da UA Madre**
  - Campo dropdown per selezione UA Madre non verificato nell'UI (edit.vue, ??? come viene popolato l'elenco?)
  - Serve mapping tra UA virtuali e UA madri: dove viene gestito? (`useSimulatore.js` ???)
  - Verificare se la selezione UA Madre influenza i calcoli downstream (??? impatto su generateCalendario?)
  - File da verificare: `/pages/revd/admin/clienti/simulatore/view/edit.vue`, `/stores/useSimulatore.js`

- **Punto 2: Date e Periodi - aperture/chiusure multiple categorie**
  - Gestione in `useDateStrutturaStore.js` (verificare se supporta più periodi/categorie ???)
  - UI: esiste una sezione per aggiungere/rimuovere periodi multipli? (edit.vue ???)
  - Impatto su calcoli occupazione e disponibilità: verificare se la logica di aggregazione è corretta (??? manca test su edge case?)

- **Punto 3: Forzare numero totale UA per sigla**
  - Campo input nell'UI per inserimento manuale totali UA (edit.vue, ??? campo già presente?)
  - Override del conteggio automatico (dove viene gestito? `useSimulatore.js` ???)
  - Casi d'uso: Camping, strutture con variazioni stagionali, ???
  - Validazione: cosa succede se il valore manuale è incoerente con i dati storici? (??? gestione errori?)

- **Punto 4: Inserire valore totale sigle UA manualmente**
  - Campo per inserire totale al netto disponibilità agenzie (edit.vue, ??? campo già presente? dove viene salvato?)
  - Logica per calcoli occupazione con totali custom (funzioni coinvolte: ???)
  - Necessario validare che il totale sia coerente con la somma delle disponibilità (???)

**Priorità Media:**
- **Punto 8: Riga evidenza Eventi**
  - Indicatore visivo nel calendario quando c'è un evento (UI: ??? esiste già? quale componente lo gestisce?)
  - Modifica UI del simulatore (edit.vue, ??? serve nuovo componente o modifica di uno esistente?)
  - Collegamento con gestione eventi in `useEventiStore.js` (???)

- **Punto 9: Mappatura importazione prenotazioni**
  - Sistema per mappare vecchie sigle con nuove (`usePrenotazioniDownloadStore.js`, ??? logica già presente o da implementare?)
  - UI: esiste una sezione per la mappatura? (??? serve un wizard/manual mapping?)
  - Gestione edge case: sigle non trovate, mapping incompleto (???)

### 15.3 Funzionalità da Rimuovere 

**Da eliminare come da specifiche:**
- **Punto 5: Prezzi Fascia Oraria** - Da rimuovere completamente (??? funzioni/variabili ancora presenti in `useSimulatore.js`?)
- **Punto 6: Prezzi infrasettimanali** - Da rimuovere completamente  (??? dove sono usati? solo in UI o anche in store?)
- **Punto 7: Tabella e calcolo Release** - Da rimuovere completamente (??? logica dispersa in più file?)

### 15.4 File coinvolti nell'implementazione

**Store Pinia:**
- `/stores/useSimulatore.js` - Store principale (controllare funzioni: generateCalendario, determinaCostoFocus, calcolaCosto, calcolaPrezzoMup, calculatePrezzoRevenue, ???)
- `/stores/useDateStrutturaStore.js` - Gestione date e periodi (??? funzioni per aperture/chiusure multiple)
- `/stores/useSigleuaStore.js` - Gestione sigle UA (??? mapping UA virtuali/madri)
- `/stores/usePrenotazioniDownloadStore.js` - Importazione prenotazioni (gestione mapping sigle, ??? edge case?)
- `/stores/useMappaSigleuaStore.js` - Mappatura sigle (??? come viene usato nei calcoli?)
- `/stores/useEventiStore.js` - Gestione eventi (??? collegamento indicatori UI)

**Componenti UI:**
- `/pages/revd/admin/clienti/simulatore/view/edit.vue` - Pagina principale simulatore (verificare presenza campi, logica override, ???)
- `/components/simulatore/` - Componenti specifici (da verificare esistenza, ??? naming e struttura)
- Altri componenti calendar/eventi? (??? serve lista completa)

### 15.5 Azioni Immediate Richieste

1. **Verifica UI**: Controllare che tutti i campi richiesti siano presenti nell'interfaccia (dropdown UA Madre, input override UA, indicatori eventi, ???)
2. **Testing calcoli**: Verificare correttezza formule con dati di test (??? casi limite coperti? serve test automatizzato?)
3. **Rimozione features**: Eliminare codice relativo a fascia oraria, infrasettimanali, release (??? dipendenze incrociate?)
4. **Mappatura UA Madre**: Implementare dropdown selezione UA Madre per virtuali (??? serve validazione?)
5. **Override manuali**: Aggiungere campi per forzare totali UA e valori (??? gestione rollback/undo?)

### 15.6 Note Tecniche e Domande Aperte

- Mapping puntuale tra specifiche (CALCOLI SIMULATORE.md) e funzioni/store/componenti: ??? (serve tabella dettagliata)
- Quali test automatici/unitari sono già presenti? (??? nessuno trovato nei file visti)
- La gestione edge case (es. periodi sovrapposti, sigle non trovate, override incoerenti) è coperta? ???
- Serve documentazione dettagliata per onboarding nuovi dev? ???
- Alcuni nomi di variabili/funzioni non sono autoesplicativi: serve refactoring? ???
- Esistono dipendenze non documentate tra store? ???
- Serve validazione extra lato UI per input manuali? ???
- La logica di calcolo occupazione tiene conto di tutte le variabili (manuali, eventi, aperture)? ???
- Serve una board Kanban condivisa per tracciare i progressi? ???
3. **Rimozione features**: Eliminare codice relativo a fascia oraria, infrasettimanali, release
4. **Mappatura UA Madre**: Implementare dropdown selezione UA Madre per virtuali
5. **Override manuali**: Aggiungere campi per forzare totali UA e valori

### 15.6 Note Tecniche

**Modello usato:** GPT-4  
**Token in input:** ~3.2K  
**Token in output:** ~800

**Priorità implementazione:**
1. Completare UI per funzionalità mancanti (Punti 1,2,3,4)
2. Rimuovere funzionalità deprecate (Punti 5,6,7)  
3. Aggiungere indicatori eventi (Punto 8)
4. Implementare mappatura prenotazioni (Punto 9)

## Note importanti

---

## 16. Piano Dettagliato Analisi e Refactoring Simulatore (Task 100+)

### 100. Analisi delle Specifiche e Raccolta Requisiti
- [x] 100.1 Leggere e sintetizzare tutte le funzionalità richieste in CALCOLI SIMULATORE.md
- [x] 100.2 Evidenziare punti ambigui o non completamente descritti
- [x] 100.3 Produrre una tabella di mapping tra requisiti e punti di codice/store/componenti coinvolti
- [x] 100.4 (Criterio completamento) Documento di mapping e chiarimenti raccolti

### 101. Verifica Implementazione Attuale
- [x] 101.1 Analizzare useSimulatore.js e tutti gli store collegati (useCostiStore.js, useEventiStore.js, ecc.)
- [ ] 101.2 Per ogni funzione di calcolo (costo, MUP, revenue, eventi, occupazione, override manuali), indicare:
  - 101.2.1 Dove si trova la logica
  - 101.2.2 Se la formula corrisponde alle specifiche
  - 101.2.3 Se sono presenti test o validazioni
- [ ] 101.3 Annotare eventuali discrepanze, mancanze o implementazioni parziali
- [ ] 101.4 (Criterio completamento) Tabella di verifica con note per ogni funzione

### 102. Analisi UI/UX e Copertura Funzionalità
- [ ] 102.1 Verificare che tutte le funzionalità abbiano un corrispettivo nell’interfaccia utente (es. dropdown UA Madre, input override UA, indicatori eventi)
- [ ] 102.2 Elencare le parti di UI mancanti o incomplete
- [ ] 102.3 Verificare l’accessibilità e l’usabilità dei nuovi campi richiesti
- [ ] 102.4 (Criterio completamento) Lista dettagliata delle mancanze UI/UX

### 103. Refactoring e Migliorie Codice
- [ ] 103.1 Proporre refactoring per funzioni duplicate, codice poco chiaro o difficile da mantenere
- [ ] 103.2 Suggerire l’uso di costanti, tipi, validazioni e commenti dove mancano
- [ ] 103.3 Proporre l’estrazione di logiche complesse in helper dedicati
- [ ] 103.4 (Criterio completamento) Proposta di refactoring documentata

### 104. Eliminazione Funzionalità Deprecated
- [ ] 104.1 Identificare e pianificare la rimozione di:
  - 104.1.1 Prezzi fascia oraria
  - 104.1.2 Prezzi infrasettimanali
  - 104.1.3 Tabella/calcolo Release
  - 104.1.4 Sezione "Prenotazioni" non più richiesta
  - 104.1.5 Trattamenti per UA non più richiesti
- [ ] 104.2 Elencare file e componenti da modificare o eliminare
- [ ] 104.3 (Criterio completamento) Lista file da modificare/eliminare e piano di rimozione

### 105. Testing e Validazione
- [ ] 105.1 Definire casi di test per ogni calcolo principale (costo, MUP, revenue, override manuali)
- [ ] 105.2 Pianificare test di integrazione tra store e UI
- [ ] 105.3 Suggerire strumenti di test automatici se non presenti
- [ ] 105.4 (Criterio completamento) Elenco casi di test e strumenti scelti

### 106. Documentazione e Knowledge Sharing
- [ ] 106.1 Aggiornare la documentazione tecnica per ogni funzione/store modificato
- [ ] 106.2 Produrre esempi d’uso e casi limite per ogni funzione complessa
- [ ] 106.3 Aggiornare o creare README/guide per sviluppatori
- [ ] 106.4 (Criterio completamento) Documentazione aggiornata e condivisa

### 107. Roadmap e Priorità
- [ ] 107.1 Ordinare le attività in base a impatto e dipendenze (prima UI e override, poi rimozione, poi refactoring profondo)
- [ ] 107.2 Stimare tempi/effort per ciascun macro-task
- [ ] 107.3 Evidenziare quick win e attività bloccanti
- [ ] 107.4 (Criterio completamento) Roadmap con stime e priorità

### 108. Monitoraggio Avanzamento
- [ ] 108.1 Definire criteri di completamento per ogni task
- [ ] 108.2 Proporre una board Kanban o checklist condivisa
- [ ] 108.3 (Criterio completamento) Board condivisa e aggiornata

---
1. Questa è una mappatura preliminare e alcuni file potrebbero richiedere ulteriore analisi per identificare con precisione dove eseguire le modifiche.
2. Per ogni modifica è necessario verificare quali componenti sono utilizzati nei file Vue e quali store Pinia sono coinvolti.
3. Alcuni interventi potrebbero richiedere la creazione di nuovi file o componenti.
4. I calcoli relativi a costi, prezzi e logiche di business richiederanno un'attenta revisione degli algoritmi esistenti.

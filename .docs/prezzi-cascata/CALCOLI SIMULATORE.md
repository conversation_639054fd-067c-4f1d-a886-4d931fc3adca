   
**CALCOLI SIMULATORE**

1. In sigle UA è necessario specificare che la Ua virtuale sia derivata di Quale UA Madre (inserire campo a discesa da selezionare) \- 	Vedi punto 10  
2. In Date e Periodi, aggiungere aperture e chiusure se si inseriscono più di una Categoria  
3. Rendere possibile di poter forzare di inserire il numero totale delle UA per sigla UA. C’è un caso in cui non si inseriscono le UA ma solo le sigle (vedi Camping) quindi QuoProfit non può fare il conto di quante sono le sigle UA allora in questo caso c’è bisogno di inserire i totali a mano  
4. Rendere possibile di inserire il valore totale delle sigle UA a mano. C’è un caso in cui ad esempio nei campeggi si mappano le UA a Forfait o da Contratto con Agenzie per creare prezzo ma i calcoli sono al netto della disponibilità data alla Agenzie quindi il totale numero UA non deve essere il totale reale ma il totale al netto della disponibilità fornita alle agenzie. Questo serve per calcolare esattamente l’occupazione in funzione delle prenotazioni, perchè nelle prenotazioni non ci saranno mai quelle vendute da contratto perchè non passano dal gestionale o dal Channel Manager  
5. **Eliminare** prezzi Fascia Oraria  
6. **Eliminare** prezzi infrasettimanali  
7. **Eliminare** Tabella e calcolo Release  
8. In qualche modo inserire in alto nel simulatore una riga in cui si evidenzi se in una certa data c’è un Evento o no  
9. \[solo per ricordare\]: In qualche parte consentire di mappare nell’importazione delle prenotazioni la sigla UA se l’importazione utilizza vecchi sigle o altre di modo che possa essere riconosciuta  
10. \[solo per ricordare\]: Nel caso in cui ci siano delle virtuali mappate con codice Channel, penso sia importante associarle alla UA Madre in quanto il calcolo della disponibilità dipende dal riconoscimento di quale debba essere la Sigla UA madre da defalcare e scalare a prescindere da quale UA virtuale si  stata venduta

    SIMULATORE

    

1. Costo  
2. Prezzo MUP  
3. Prezzo Revenue  
4. Prezzo

1. **Costo** (Voce nel Simulatore per sigla UA e data)

   I costi devono essere sempre Ivati al 10%, dipende se sono inseriti con o senza IVA, \-\> trasformarli o lasciarli con iva

		Calcolo:  
		  
(((Valore del Costo totale per Sigla UA)\*(peso in punti per sigla UA per Data da utilizzare)/(numero massimo sigla UA))  
**PCUAD)** (peso categoria UA per Data)  
**CTSUA** Costo totale per Sigla UA

**(CTSUA\*PCUAD)/(**numero massimo sigla UA)

Il Valore totale dei costi della Struttura è Costi+Costi Eventi+ Valore Disoccupazione €: (Costi Fissi+Costi Marginali)+(somma Costi Eventi)+(valore in € della percentuale di disoccupazione sul totale Costi (Costi Fissi+ Costi Variabili)   
quoprofit: **costo\_totale**  
Vedi link Hotel San Michele (impostazione gestione costi Automatica)  
[https://docs.google.com/spreadsheets/d/1XLN-djJYq0MjCd7seKM7rqfnuq9WG2UaMllAL1sqn8M/edit?gid=1929474367\#gid=1929474367](https://docs.google.com/spreadsheets/d/1XLN-djJYq0MjCd7seKM7rqfnuq9WG2UaMllAL1sqn8M/edit?gid=1929474367#gid=1929474367)

Vedi link Villaggio SF (impostazione gestione costi Manuale)  
[https://docs.google.com/spreadsheets/d/1n9GoD-Tp\_dWx9\_ErYpHSBEusGo8tK149e10YtDPynA4/edit?gid=1929474367\#gid=1929474367](https://docs.google.com/spreadsheets/d/1n9GoD-Tp_dWx9_ErYpHSBEusGo8tK149e10YtDPynA4/edit?gid=1929474367#gid=1929474367)

1. **Peso per Sigla UA** (vedi foglio Gestione\_Costi cella C18)  
   1. Peso per Sigla UA (Automatico)  
      1. (numero UA per sigla UA)\*(prezzo minimo Sigla UA))) \-\> foglio Ins\_UA cella AF9  
         2. 100\*((numero UA per sigla UA)\*(prezzo minimo Sigla UA))/(somma dei valori di tutte le sigle UA del calcolo precedente  
            2. Peso per Sigla UA (Manuale)  
               1. Inserimento manuale per Categoria UA del valore del peso  
               2. ((100\*(valore peso automatico UA))/(somma dei pesi automatici per sigla UA) \-\> foglio Gestione\_Costi cella c18  
      2. **CTSUA**: Costo totale per Sigla UA

         1. ## **peso\_su\_tipologia\_ua\_auto**

         2. Valore Costo per Sigla UA (Automatico) **costo\_su\_tipologia\_ua\_auto**  
            1. Valore costi pesato in funzione al peso per sigla UA (automatico)  
         3. Valore Costo per Sigla UA (Manuale)  
            1. Valore costi pesato in funzione al peso per sigla UA (Manuale)  
      3. Peso per Sigla UA e Data  
         1. Inserire valore peso (da 1 a 10\) su località \- Il dato va normalizzato su scale %  
         2. Inserire in “Date e Periodi \- Apertura e chiusura anche aperture e chiusure se ci sono più Categorie UA  
         3. Da prenotazioni recuperare occupazione %  Struttura Ricettiva totale e per Sigla UA  
         4. Per ogni categoria UA, Determinare il valore massimo tra il valore del peso inserito a mano per Categoria UA e valore di occupazione % Struttura **PCUAD** (peso categoria UA per Data). Il Valore massimo lo si trasforma in valore percentuale (**PSD**).  
         5. **PSD** (Peso % per Sigla su Data): Calcolare il peso in percentuale per Sigla UA, coincidente con la Categoria a cui appartiene, in funzione dei giorni di apertura della stessa Categoria UA  
2. **Prezzo MUP** (Voce nel Simulatore per sigla UA e data)

   Si devono prima considerare i seguenti parametri e variabili:

   

   * Ricavo Anno precedente (inserisce cliente)  
   * Ricavo Aggiuntivo (inserisce il cliente)  
   * Coefficiente Disoccupazione Inserisce Cliente)  
   * MOL Reale (dato calcolato)  
     Valore MOL Reale su sigla UA su data: ( (100\* ( (Costi Fissi+Costi Marginali) \+ (somma Costi Eventi) \+ (valore in € della percentuale di disoccupazione sul totale Costi Fissi più i Costi Variabili)))/(Ricavi anno precedente))\*(**PSD**)  
   * MOL Auspicato  (dato calcolato)  
     Valore MOL Auspicato su sigla UA su data: (100\* ( (Costi Fissi+Costi Marginali) \+ (somma Costi Eventi) \+ (valore in € della percentuale di disoccupazione sul totale Costi Fissi più i Costi Variabili)))/((Ricavi anno precedente+Ricavo Aggiuntivo)\*(**PSD**))  
   * MOL Libero (dato che inserisce il cliente)  
     Valore inserito a mano \- se non c’è valore si deve selezionare MOL Reale o MOL Auspicato altrimenti se c’è valore si prende il valore MOL Libero  
     Valore MOL Libero su Sigla UA su data: MOL libero\*(**PSD**)  
     

	Calcolo Prezzo MUP: Costo per sigla su data\*(MOL) \-\> il MOL utilizzato è quello selezionato o libero (quello forzato vince)

3. **Prezzo Revenue** (Voce nel Simulatore per sigla UA e data)

   * Valore Occupazione percentuale su data per sigla UA. Se il valore dell’occupazione per sigla UA su Data è inferiore al valore a cui è stato associato il peso in punti 1 ( il valore può essere modificato per ogni struttura dall’Amministratore):  
     1. In funzione del valore di Occupazione su Data per Sigla UA   
        Prezzo MUP\*(valore tabella Variazione prezzo in Punti in funzione valore occupazione%)  
        NB: i valori di variazione prezzo della tabella possono essere modificati per ogni struttura dall’Amministratore  
         i valori di variazione prezzo della tabella possono essere modificati per ogni struttura dall’Amministratore

4. **Prezzo** (Voce nel Simulatore per sigla UA e data)  
   * Modifiche Manuali per sigla UA  
     1. Minimo Garantito €  
     2. Modifica Prezzo in %  
     3. Prezzo Forzato in €  
   * Modifiche Manuali per data su tutte Sigle UA  
     1. Modifica Prezzo in %  
     2. Selezione Piano Tariffario  
        1. Costo  
        2. Prezzo MUP  
        3. Minimo Garantito € (se 0 \-\> MUP)  
        4. Prezzo Revenue  
        5. RMC (ADR) (se \< Valore Costo \-\> MUP)  
5. **Prenotazioni**  
   * Prenotazioni da esportazione Estesa o Prenotazioni da esportazione Sintetica  
     1. dati tabella da prenotazioni  
        1. n° Prenotazione (numero sequenziale costruito in Google Sheet. Es: 1,2,3…)  
        2. Data Ospitalità  
           Sia per le prenotazioni estese che sintetiche si deve estrarre la data di ospitalità che ci sarà già in ogni rigs nel caso in cui l’esportazione delle prenotazioni siano Estese, altrimenti si devono creare se l’esportazione delle prenotazioni è sintetica, nel senso che se è sintetica ci sarà una sola riga per prenotazione e si dovrà esplodere in una riga per ogni data di ospitalità  
        3. Tipologia Sigla  
           Si deve recuperare la sigla UA per ogni data ospitalità. Si ricorda che la sigla UA la si recupera dalla sigla prenotata presente nel file di prenotazioni che può diventare sigla assegnata (in quanto l’operatore può decidere di spostare ed assegnare altra sigla UA ma soprattutto se la sigla UA delle prenotazioni non esiste nella mappatura il sistema deve andare ad assegnare la sigla UA inserita a mano nella nuova associazione  
        4. Prezzo per notte  
           E’ il prezzo della notte di ogni prenotazione. Se l’esportazione è estesa ogni data ospitalità (notte) può avere las ua specifica cifra (Prezzo). se l’esportazione è sintetica, si calcola il valore complessivo della prenotazione e lo si divide per il numero notti di soggiorno e si recupera il prezzo notte.  
        5. Prezzo per Trattamento complessivo  
           Per ogni notte prenotazione si individua nel file delle prenotazioni il tipo di trattamento e in funzione del numero di persone in quello specifico trattamento in quella notte- giorno e si calcola il valore complessivo per giorno del valore totale del trattamento in base al numero di persone prenotanti  
     2. Calcolo Prenotazioni e calcolo Occupazione per sigla UA  
        1. Calcolo numero UA vendute per ogni sigla UA per ogni data  
           ![][image1]  
           1. calcolo percentuale per data per sigla (in funzione del numero massimo disponibile per sigla UA si calcola quant’è il valore percentuale di occupazione per sigla UA per ogni data. Questo valore (confrontato con il valore percentuale per data inserito a mano dall’albergatore come peso per data, ricordando che si dovrà prendere quello più alto, sarà utilizzato nel simulatore per ogni sigla UA per pesare il costo totale perogni sigla UA per definire il costo per sigla e per data  
        2. Calcolo ricavi per sigla UA e per data (compreso di trattamento)  
        3. Calcolo ricavi per sigla UA  e per data senza il valore del trattamento incassato (praticamente solo il valore UA

**Indicatori**

* Costo			da calcoli	  
* Prezzo MUp		da calcoli  
* Prezzo Revenue		da calcoli  
* Concorrenza		da tabella  
* Occupazione %		da calcoli su prenotazioni (sulla singola sigla UA)  
* Profitto			calcolo ... alla data:( (somma dei ricavi per sigla meno somma costi per sigla)/numero giorni

  di apertura fino a quel momento)/n° sigla UA

* RMC \- ADR		da calcoli (per ogni data è dato da somma dei ricavi su sigla diviso il numero delle sigle vendute)  
* RevPar			da calcoli (per ogni data è dato da somma dei ricavi su sigla diviso il numero delle sigle totali)  
* Prezzo Garantito		inserimento in €  
* Modifica Prezzo % 	Inserimento in %  
* Prezzo Forzato		Inserimento in €  
* **Prezzo**			da calcoli (vedi sotto logica)

Se il Prezzo è inferiore al “Prezzo Garantito” il Prezzo diventa “Prezzo Garantito”  
Se esiste “Prezzo Forzato” il “Prezzo” diventa “Prezzo Forzato”

Logica e sequenzialità determinazione “Prezzo”

Evoluzione Prezzo: Prezzo 1, Prezzo 2, Prezzo 3, Prezzo 5, Prezzo 6

**Costo**

* Dal “Costo” determino il “**Prezzo MUP**”  
* Dal MUP, in base alla tabella di modifica in percentuale in base all’occupazione,  determino il “**Prezzo Revenue**” e il “**Prezzo Revenue**” fa cambiare il valore del “**Prezzo 1**”  
* La selezione del “**Piano Tariffario**” sostituisce “**Prezzo 1**” e determina il “**Prezzo 2**”  
* La “Modifica %” determina il **Prezzo 3,** basato sul **Prezzo 1** o **Prezzo 2**  
* La **Modifica % sulla singola sigla** calcolata sul **Prezzo 3** determina il **Prezzo 4**  
* Se c’è, il **“Prezzo Garantito”,** controlla il Prezzo 4 lasciandolo com’è se maggiore del **“Prezzo Garantito”** o facendolo coincidere se è minore, determinando il **Prezzo 5**  
* Se c’è il **Prezzo Forzato,** lo stesso sostituisce il valore del **Prezzo 5** determinando il **Prezzo 6** che Coincide con il **Prezzo**

  **if(GK$816=0;"";(($NG5\*vlookup((vlookup($C5;Gestione\_Costi\!$B$18:$F$57;5;false));$D$806:$NF$815;GK$805;false))/100)/vlookup($C5;Ins\_UA\!$AC$9:$AD$48;2;false))**

  if(GK$816=0;"";

  se **il valore percentuale massimo** tra il peso della data inserito a mano e il valore percentuale dell’occupazione sulla stessa data determinato dal valore dell’occupazione derivante dalle prenotazioni è **zero** il costo per sigla è **zero** altrimenti 


  (($NG5\*vlookup((vlookup($C5;Gestione\_Costi\!$B$18:$F$57;5;false));$D$806:$NF$815;GK$805;false))/100)/vlookup($C5;Ins\_UA\!$AC$9:$AD$48;2;false)


  (vlookup($C5;Gestione\_Costi\!$B$18:$F$57;5;false)) valore 


[image1]: <data:image/png;base64,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****************************************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>
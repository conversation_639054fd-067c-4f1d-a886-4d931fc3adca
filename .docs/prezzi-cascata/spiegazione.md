# Flusso di interazione dell'interfaccia utente con la logica dei prezzi a cascata

Questo documento spiega cosa succede quando l'utente interagisce con gli elementi dell'interfaccia utente nella pagina di simulatore (`edit.vue`) e come queste interazioni influenzano il calcolo dei prezzi a cascata.

## Panoramica generale

Quando un utente modifica un valore nell'interfaccia del simulatore, viene attivata una serie di passaggi:

1. Viene chiamata la funzione `handleChangeValue` che gestisce diverse tipologie di input
2. Il valore modificato viene salvato nello store Pinia tramite la funzione `saveValue`
3. Viene eseguita la funzione `changePrezzoByInput` che attiva il ricalcolo del calendario
4. Il ricalcolo utilizza la funzione `calcolaPrezziCascata` per generare tutti i prezzi intermedi

## Tipi di input e loro effetto

### Select "Tipo tariffa" (colonna)

```html
<select
  v-model.lazy="calendario[key].tipoTariffa"
  class="combo-select"
  @change="handleChangeValue(key, null, 'tipotariffa', $event.target.value)"
>
```

Quando si modifica questa select:

1. Viene chiamato `handleChangeValue(key, null, 'tipotariffa', value)`
2. Questo attiva `handleComboChange(key, value)` che imposta il tipo tariffa per la colonna
3. **Impatto globale**: Questa modifica influenza TUTTE le sigle per quella data specifica
4. Il tipo tariffa influisce sul **Prezzo 1** nella cascata di ogni sigla:
   - `C`: Usa direttamente il costo come prezzo base per tutte le sigle
   - `M`: Calcola il prezzo MUP (costo * MOL + costo) per tutte le sigle
   - `R`: Calcola il prezzo Revenue (MUP * coefficiente occupazione) per tutte le sigle

### Input "Modifica %" (colonna)

```html
<input
  @change="handleChangeValue(key, null, 'modificaperccol', $event.target.value)"
```

**Nota importante:**

Sia la Select "Tipo tariffa" (colonna) che l'Input "Modifica %" (colonna) hanno effetto su tutti i prezzi della colonna per tutte le diverse sigle UA. Questo significa che una modifica a questi controlli applica la variazione a tutte le sigle per quella data/colonna, garantendo coerenza tra i prezzi delle diverse sigle UA nella stessa colonna.

  v-model="calendario[key].modificaPerc"
  class="input-perc-colonna"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, null, 'modificaperccol', value)`
2. **Impatto globale**: Questa modifica influenza TUTTE le sigle per quella data specifica
3. Il valore viene utilizzato durante il ricalcolo per modificare il **Prezzo 3** nella cascata di ogni sigla:
   - La percentuale viene applicata come moltiplicatore del Prezzo 2 per tutte le sigle della colonna

### Input "Garantito" (per sigla)

```html
<input
  @change="handleChangeValue(key, sigla, 'garantito', $event.target.value)"
  :value="calendario[key] ? calendario[key].sigle.find(s => s.siglaUa === sigla)[paramIndex] : ''"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, sigla, 'garantito', value)`
2. Il valore viene salvato tramite `saveValue(key, sigla, 'garantito', value)`
3. Questo influisce sul **Prezzo 5** nella cascata:
   - Se prezzo4 < garantito, allora prezzo5 = garantito

### Input "Modifica %" (per sigla)

```html
<input
  @change="handleChangeValue(key, sigla, 'modificaperc', $event.target.value)"
  :value="calendario[key] ? calendario[key].sigle.find(s => s.siglaUa === sigla)[paramIndex] : ''"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, sigla, 'modificaperc', value)`
2. Il valore viene salvato tramite `saveValue(key, sigla, 'modificaperc', value)`
3. Questo influisce sul **Prezzo 4** nella cascata:
   - La percentuale viene applicata come moltiplicatore del Prezzo 3

### Input "Prezzo forzato" (per sigla)

```html
<input
  @change="handleChangeValue(key, sigla, 'prezzoforzato', $event.target.value)"
  :value="calendario[key] ? calendario[key].sigle.find(s => s.siglaUa === sigla)[paramIndex] : ''"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, sigla, 'prezzoforzato', value)`
2. Il valore viene salvato tramite `saveValue(key, sigla, 'prezzoforzato', value)`
3. Questo influisce sul **Prezzo 6** nella cascata:
   - Se prezzoforzato > 0, allora prezzo6 = prezzoforzato (override totale)

## Flusso di elaborazione completo

```
Modifica UI → handleChangeValue() → saveValue() → changePrezzoByInput() → 
generateCalendario() → [loop su date e sigle] → calcolaPrezziCascata() → 
aggiornamento siglaData con nuovi prezzi
```

## Dettaglio funzione calcolaPrezziCascata

La funzione `calcolaPrezziCascata` riceve i seguenti parametri:

- `costo`: Costo calcolato per la sigla/data
- `molReale` e `molForzato`: MOL applicati
- `disoccupazione`: Percentuale occupazione
- `tipoTariffa`: Tipo tariffa selezionato (C/M/R)
- `modificaPerc`: Percentuale modifica colonna
- `modificaPercSigla`: Percentuale modifica sigla
- `garantito`: Prezzo garantito (minimo)
- `forzato`: Prezzo forzato (override)
- `prezzoBase`: Prezzo base
E produce i seguenti output:

- `prezzo1`: Prezzo base (da tariffa C/M/R)
- `prezzo2`: Prezzo con piano tariffario (attualmente uguale a prezzo1)
- `prezzo3`: Prezzo dopo modifica % colonna
- `prezzo4`: Prezzo dopo modifica % sigla
- `prezzo5`: Prezzo dopo garantito
- `prezzo6`: Prezzo finale (dopo forzato)
- `profitto`: Prezzo finale - costo
- `prezzoFinale`: Alias di prezzo6 per compatibilità

## Impatto delle modifiche sulla visualizzazione

Dopo che le modifiche sono state elaborate, i nuovi valori vengono visualizzati nella griglia del simulatore. La funzione `getSiglaParamValue` recupera i valori appropriati da visualizzare per ogni cella.

## Persistenza dei dati

I dati modificati vengono salvati in IndexedDB attraverso la chiamata:
```javascript
simulatoreStore.saveCalendarioToIndexedDB(toRaw(simulatoreStore.calendario));
```

Questo garantisce che le modifiche persistano anche dopo il reload della pagina.

# Flusso di interazione dell'interfaccia utente con la logica dei prezzi a cascata

Questo documento spiega cosa succede quando l'utente interagisce con gli elementi dell'interfaccia utente nella pagina di simulatore (`edit.vue`) e come queste interazioni influenzano il calcolo dei prezzi a cascata, con particolare focus sui valori `aprezzo` e la funzione `finalPriceN`.

## Panoramica generale

Quando un utente modifica un valore nell'interfaccia del simulatore, viene attivata una serie di passaggi:

1. Viene chiamata la funzione `handleChangeValue` che gestisce diverse tipologie di input
2. Il valore modificato viene salvato nello store Pinia tramite la funzione `saveValue`
3. Viene eseguita la funzione `changePrezzoByInput` che attiva il ricalcolo del calendario
4. Il ricalcolo utilizza la funzione `calcolaPrezziCascata` per generare tutti i prezzi intermedi
5. **IMPORTANTE**: <PERSON><PERSON> i valori `aprezzo`, `aprezzo1`-`aprezzo8` vengono ricalcolati utilizzando la funzione `finalPriceN` con il nuovo `prezzoCorrenteDaModifiche`

## Tipi di input e loro effetto

### Select "Tipo tariffa" (colonna)

```html
<select
  v-model.lazy="calendario[key].tipoTariffa"
  class="combo-select"
  @change="handleChangeValue(key, null, 'tipotariffa', $event.target.value)"
>
```

Quando si modifica questa select:

1. Viene chiamato `handleChangeValue(key, null, 'tipotariffa', value)`
2. Questo attiva `handleComboChange(key, value)` che imposta il tipo tariffa per la colonna
3. **Impatto globale**: Questa modifica influenza TUTTE le sigle per quella data specifica
4. Il tipo tariffa influisce sul **Prezzo 1** nella cascata di ogni sigla:
   - `C`: Usa direttamente il costo come prezzo base per tutte le sigle
   - `M`: Calcola il prezzo MUP (costo * MOL + costo) per tutte le sigle
   - `R`: Calcola il prezzo Revenue (MUP * coefficiente occupazione) per tutte le sigle

### Input "Modifica %" (colonna)

```html
<input
  @change="handleChangeValue(key, null, 'modificaperccol', $event.target.value)"
```

**Nota importante:**

Sia la Select "Tipo tariffa" (colonna) che l'Input "Modifica %" (colonna) hanno effetto su tutti i prezzi della colonna per tutte le diverse sigle UA. Questo significa che una modifica a questi controlli applica la variazione a tutte le sigle per quella data/colonna, garantendo coerenza tra i prezzi delle diverse sigle UA nella stessa colonna.

  v-model="calendario[key].modificaPerc"
  class="input-perc-colonna"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, null, 'modificaperccol', value)`
2. **Impatto globale**: Questa modifica influenza TUTTE le sigle per quella data specifica
3. Il valore viene utilizzato durante il ricalcolo per modificare il **Prezzo 3** nella cascata di ogni sigla:
   - La percentuale viene applicata come moltiplicatore del Prezzo 2 per tutte le sigle della colonna

### Input "Garantito" (per sigla)

```html
<input
  @change="handleChangeValue(key, sigla, 'garantito', $event.target.value)"
  :value="calendario[key] ? calendario[key].sigle.find(s => s.siglaUa === sigla)[paramIndex] : ''"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, sigla, 'garantito', value)`
2. Il valore viene salvato tramite `saveValue(key, sigla, 'garantito', value)`
3. Questo influisce sul **Prezzo 5** nella cascata:
   - Se prezzo4 < garantito, allora prezzo5 = garantito

### Input "Modifica %" (per sigla)

```html
<input
  @change="handleChangeValue(key, sigla, 'modificaperc', $event.target.value)"
  :value="calendario[key] ? calendario[key].sigle.find(s => s.siglaUa === sigla)[paramIndex] : ''"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, sigla, 'modificaperc', value)`
2. Il valore viene salvato tramite `saveValue(key, sigla, 'modificaperc', value)`
3. Questo influisce sul **Prezzo 4** nella cascata:
   - La percentuale viene applicata come moltiplicatore del Prezzo 3

### Input "Prezzo forzato" (per sigla)

```html
<input
  @change="handleChangeValue(key, sigla, 'prezzoforzato', $event.target.value)"
  :value="calendario[key] ? calendario[key].sigle.find(s => s.siglaUa === sigla)[paramIndex] : ''"
/>
```

Quando si modifica questo input:

1. Viene chiamato `handleChangeValue(key, sigla, 'prezzoforzato', value)`
2. Il valore viene salvato tramite `saveValue(key, sigla, 'prezzoforzato', value)`
3. Questo influisce sul **Prezzo 6** nella cascata:
   - Se prezzoforzato > 0, allora prezzo6 = prezzoforzato (override totale)

## Algoritmo di calcolo dei prezzi a cascata

### Struttura dei valori aprezzo

Il sistema mantiene i seguenti valori per ogni sigla/data:

- `aprezzo_ori`: Prezzo originale senza modifiche (finalPrice iniziale)
- `aprezzo`: Prezzo base (finalPriceN(0)) - equivale al prezzo per 1 persona
- `aprezzo1`: Prezzo per 1 persona aggiuntiva (finalPriceN(1))
- `aprezzo2`: Prezzo per 2 persone aggiuntive (finalPriceN(2))
- `aprezzo3`: Prezzo per 3 persone aggiuntive (finalPriceN(3))
- `aprezzo4`: Prezzo per 4 persone aggiuntive (finalPriceN(4))
- `aprezzo5`: Prezzo per 5 persone aggiuntive (finalPriceN(5))
- `aprezzo6`: Prezzo per 6 persone aggiuntive (finalPriceN(6))
- `aprezzo7`: Prezzo per 7 persone aggiuntive (finalPriceN(7))
- `aprezzo8`: Prezzo per 8 persone aggiuntive (finalPriceN(8))

### Logica di priorità per il calcolo del prezzoCorrenteDaModifiche

Quando vengono inseriti valori negli input box, l'algoritmo a cascata determina quale prezzo utilizzare secondo questa priorità:

1. **PRIORITÀ MASSIMA - Prezzo Forzato**: Se `prezzoforzato > 0`, questo valore sovrascrive tutto
2. **PRIORITÀ ALTA - Prezzo Garantito**: Se il prezzo calcolato è inferiore a `garantito`, viene utilizzato il garantito
3. **PRIORITÀ MEDIA - Modifica Percentuale Sigla**: Applica la percentuale `modificaperc` al prezzo base
4. **PRIORITÀ BASSA - Modifica Percentuale Colonna**: Applica la percentuale `modificaperccol` a tutte le sigle della data
5. **PREZZO BASE**: Se tutti gli input sono nulli/zero, viene utilizzato `aprezzo_ori`

## Flusso di elaborazione completo

```
Modifica UI → handleChangeValue() → saveValue() → changePrezzoByInput() →
calculatePrezzoCorrenteDaModifiche() → [applica algoritmo cascata] →
aggiornamento di tutti i valori aprezzo tramite finalPriceN() →
aggiornamento siglaData con nuovi prezzi
```

## Dettaglio funzione finalPriceN

## Dettaglio funzione generateCalendario

La funzione `generateCalendario` è il cuore dell'algoritmo di calcolo dei prezzi a cascata. Viene richiamata per ricalcolare l'intero calendario dei prezzi, tenendo conto di tutte le variabili e le logiche di business.

**Flusso principale:**
1.  Inizializza il calendario.
2.  Carica le prenotazioni e le impostazioni del simulatore.
3.  Recupera la mappa dei prezzi per occupazione.
4.  Definisce e utilizza diverse funzioni helper per i calcoli specifici:
    *   `getOccupancy`: Calcola la percentuale di occupazione per una data e sigla specifica.
    *   `getModificaDaOccupazione`: Recupera la variazione di prezzo basata sull'occupazione.
    *   `applyPriceModifications`: Applica le modifiche di prezzo basate su `garantito`, `modificaperc` e `prezzoforzato`. Questa funzione implementa la logica di priorità descritta nella sezione "Logica di priorità per il calcolo del prezzoCorrenteDaModifiche".
    *   `calcolaCostoAutomaticoPerUA`: Calcola il costo automatico per unità abitativa (UA).
    *   `calcolaCosto`: Determina il costo finale per una sigla e stato specifici.
    *   `calcolaPrezzoMup`: Calcola il prezzo MUP (Mark-Up Price) basato sul costo e sul MOL (Margine Operativo Lordo).
    *   `calculatePrezzoRevenue`: Calcola il prezzo Revenue basato sul MUP e sulla disoccupazione.
    *   `computeD`: Calcola un valore 'D' basato su `nUA` e `prezzo_minimo` della sigla.
5.  Itera su ogni giorno del calendario e per ogni sigla UA, applica le logiche di calcolo per determinare i vari prezzi (`prezzoBase`, `prezziRevenueN`, `finalPriceN`, ecc.).

## Dettaglio funzione calculatePrezzoRevenue

`calculatePrezzoRevenue(mup, disoccupazione, date)` calcola il prezzo basato sul concetto di "revenue management".

-   `mup`: Il prezzo Mark-Up Price calcolato.
-   `disoccupazione`: La percentuale di disoccupazione (1 - occupazione).
-   `date`: La data di riferimento (per logging).

**Logica di calcolo:**
1.  Recupera il coefficiente di modifica (`br`) basato sulla `disoccupazione` tramite `getModificaDaOccupazione`.
2.  Calcola il prezzo revenue (`cr`) moltiplicando il `mup` per il coefficiente `br`.
3.  Ritorna il valore arrotondato a 0 cifre decimali.

## Dettaglio funzione calcolaPrezzoMup

`calcolaPrezzoMup(costoFocus, stato, molReale, molForzato)` calcola il prezzo Mark-Up Price.

-   `costoFocus`: Il costo di riferimento.
-   `stato`: Lo stato della sigla (es. "A" per attivo).
-   `molReale`: Il MOL reale.
-   `molForzato`: Il MOL forzato.

**Logica di calcolo:**
1.  Se lo `stato` non è "A", ritorna '-'.
2.  Determina il MOL da usare (`molDaUsare`) prendendo il massimo tra `molReale` e `molForzato`.
3.  Calcola il MUP come `costoFocus * molDaUsare + costoFocus`.

## Dettaglio funzione computeD

`computeD(sigla)` calcola un valore 'D' specifico per una data sigla.

-   `sigla`: L'oggetto sigla contenente `nUA` (numero di unità abitative) e `prezzo_minimo`.

**Logica di calcolo:**
1.  Estrae `nUA` e `prezzo_minimo` dalla sigla.
2.  Esegue controlli di validità per assicurarsi che siano valori numerici.
3.  Calcola il valore 'D' basato su questi parametri (la logica completa è complessa e si estende per molte righe nel codice sorgente, ma l'obiettivo è derivare un valore specifico per la sigla).

## Dettaglio funzione handleChangeValue

`handleChangeValue(key, sigla, param, value)` è la funzione principale che gestisce le modifiche provenienti dall'interfaccia utente.

-   `key`: Chiave della riga del calendario (data).
-   `sigla`: Sigla identificativa della colonna (può essere `null` per modifiche a livello di colonna).
-   `param`: Il parametro che è stato modificato (es. "tipotariffa", "modificaperccol", "garantito", "modificaperc", "prezzoforzato").
-   `value`: Il nuovo valore.

```javascript
/**
 * Gestisce il cambiamento di valore per una cella della tabella (store).
 * @param {string|number} key - Chiave della riga del calendario.
 * @param {string|null} sigla - Sigla identificativa della colonna.
 * @param {string} param - Parametro modificato.
 * @param {*} value - Nuovo valore da assegnare.
 */
handleChangeValue(key, sigla, param, value) {
    console.log('[useSimulatore] handleChangeValue');
    switch (param) {
        case "tipotariffa":
            setTimeout(() => { this.handleComboChange(key, value); }, 50);
            break;
        case "modificaperccol":
            break;
        case "garantito":
            this.saveValue && this.saveValue(key, sigla, param, value);
            break;
        case "modificaperc":
            this.saveValue && this.saveValue(key, sigla, param, value);
            break;
        case "prezzoforzato":
            this.saveValue && this.saveValue(key, sigla, param, value);
            break;
        default:
            break;
    }
    setTimeout(() => {
        this.saveCalendarioToIndexedDB && this.saveCalendarioToIndexedDB(this.calendario);
        this.changePrezzoByInput(key, sigla, param, value);
    }, 50);
}
```

**Flusso:**
1.  In base al `param` modificato, esegue azioni specifiche:
    *   `tipotariffa`: Chiama `handleComboChange` per aggiornare il tipo di tariffa.
    *   `garantito`, `modificaperc`, `prezzoforzato`: Chiama `saveValue` per persistere il valore (se la funzione `saveValue` è definita).
2.  Dopo un breve timeout, chiama `saveCalendarioToIndexedDB` per salvare lo stato aggiornato del calendario e `changePrezzoByInput` per attivare il ricalcolo dei prezzi.

## Dettaglio funzione setup

`setup(numeroCliente)` è una funzione asincrona che inizializza lo store `useSimulatore` caricando tutti i dati necessari all'avvio dell'applicazione o al cambio di cliente.

-   `numeroCliente`: L'identificativo del cliente per cui caricare i dati.

**Flusso:**
1.  Imposta il `numero_cliente` nello store.
2.  Carica in parallelo (tramite `Promise.all`) tutti i dati da diversi store Pinia (es. `costiStore`, `sigleuaStore`, `strategieClienteStore`, `eventiStore`, ecc.) e da IndexedDB (`loadCalendarioFromIndexedDB`).
3.  Processa i dati caricati (es. `processCliente`, `processDateStruttura`, `processEventi`, `processCosti`, `processSigleua`).
4.  Calcola e imposta diverse proprietà dello store basate sui dati caricati, come `peso_per_periodo`, `eventi`, `coefficiente_disoccupazione`, `sigleUa`, `costo_totale`, `mol_reale`, `mol_forzato`, ecc.
5.  Se un calendario è già presente in IndexedDB e `useCache` è `true`, lo carica, altrimenti genera un nuovo calendario chiamando `generateCalendario`.



La funzione `finalPriceN(n, finalPrice, siglaUa)` calcola il prezzo per n persone aggiuntive:

- `n`: Numero di persone aggiuntive (0-8)
- `finalPrice`: Prezzo base calcolato con l'algoritmo a cascata
- `siglaUa`: Identificativo della sigla per recuperare gli sconti specifici

**Logica di calcolo:**
- Se `n === 0`: Ritorna il `finalPrice` (prezzo base per 1 persona)
- Se `n > 0`: Calcola il prezzo per persona aggiuntiva applicando gli sconti specifici della sigla

## Dettaglio funzione calcolaPrezziCascata

La funzione `calcolaPrezziCascata` riceve i seguenti parametri:

- `costo`: Costo calcolato per la sigla/data
- `molReale` e `molForzato`: MOL applicati
- `disoccupazione`: Percentuale occupazione
- `tipoTariffa`: Tipo tariffa selezionato (C/M/R)
- `modificaPerc`: Percentuale modifica colonna
- `modificaPercSigla`: Percentuale modifica sigla
- `garantito`: Prezzo garantito (minimo)
- `forzato`: Prezzo forzato (override)
- `prezzoBase`: Prezzo base

E produce i seguenti output:

- `prezzo1`: Prezzo base (da tariffa C/M/R)
- `prezzo2`: Prezzo con piano tariffario (attualmente uguale a prezzo1)
- `prezzo3`: Prezzo dopo modifica % colonna
- `prezzo4`: Prezzo dopo modifica % sigla
- `prezzo5`: Prezzo dopo garantito
- `prezzo6`: Prezzo finale (dopo forzato)
- `profitto`: Prezzo finale - costo
- `prezzoFinale`: Alias di prezzo6 per compatibilità

## Implementazione richiesta in useSimulatore.js

Per implementare correttamente l'algoritmo, è necessario modificare la funzione `changePrezzoByInput` in `useSimulatore.js`:

```javascript
/**
 * Calcola il prezzo corrente da modifiche applicando l'algoritmo a cascata
 * @param {Object} siglaData - Dati della sigla
 * @param {number} aprezzo_ori - Prezzo originale
 * @returns {number} - Prezzo calcolato secondo priorità
 */
calculatePrezzoCorrenteDaModifiche(siglaData, aprezzo_ori) {
    const { prezzoforzato, garantito, modificaperc, modificaperccol } = siglaData;

    // PRIORITÀ 1: Prezzo Forzato (override completo)
    if (prezzoforzato && Number(prezzoforzato) > 0) {
        return Number(prezzoforzato);
    }

    // Calcola prezzo base con modifiche percentuali
    let prezzoBase = Number(aprezzo_ori);

    // PRIORITÀ 2: Modifica Percentuale Colonna (se presente)
    if (modificaperccol && Number(modificaperccol) !== 0) {
        prezzoBase = prezzoBase * (1 + Number(modificaperccol) / 100);
    }

    // PRIORITÀ 3: Modifica Percentuale Sigla (se presente)
    if (modificaperc && Number(modificaperc) !== 0) {
        prezzoBase = prezzoBase * (1 + Number(modificaperc) / 100);
    }

    // PRIORITÀ 4: Prezzo Garantito (minimo)
    if (garantito && Number(garantito) > 0 && prezzoBase < Number(garantito)) {
        prezzoBase = Number(garantito);
    }

    return prezzoBase;
},

/**
 * Aggiorna tutti i valori aprezzo dopo una modifica
 * @param {string|number} key - Chiave della riga del calendario
 * @param {string} sigla - Sigla identificativa
 * @param {string} param - Parametro modificato
 * @param {*} value - Nuovo valore
 */
changePrezzoByInput(key, sigla, param, value) {
    console.log('[useSimulatore] changePrezzoByInput');
    const day = this.calendario[key];
    if (!day) return;

    const siglaData = day.sigle.find((s) => s.siglaUa === sigla);
    if (!siglaData) return;

    if (value === null || value === "") {
        siglaData[param] = "";
        // Se il valore è vuoto, ricalcola comunque tutti gli aprezzo
    } else {
        siglaData[param] = value;
    }

    // Calcola il nuovo prezzo corrente usando l'algoritmo a cascata
    const prezzoCorrenteDaModifiche = this.calculatePrezzoCorrenteDaModifiche(
        siglaData,
        siglaData.aprezzo_ori
    );

    // Aggiorna TUTTI i valori aprezzo con il nuovo prezzo
    siglaData.aprezzo = this.finalPriceN(0, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo1 = this.finalPriceN(1, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo2 = this.finalPriceN(2, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo3 = this.finalPriceN(3, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo4 = this.finalPriceN(4, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo5 = this.finalPriceN(5, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo6 = this.finalPriceN(6, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo7 = this.finalPriceN(7, prezzoCorrenteDaModifiche, sigla);
    siglaData.aprezzo8 = this.finalPriceN(8, prezzoCorrenteDaModifiche, sigla);
}
```

## Impatto delle modifiche sulla visualizzazione

Dopo che le modifiche sono state elaborate, tutti i valori `aprezzo` vengono aggiornati e i nuovi valori vengono visualizzati nella griglia del simulatore. La funzione `getSiglaParamValue` recupera i valori appropriati da visualizzare per ogni cella.

## Persistenza dei dati

I dati modificati vengono salvati in IndexedDB attraverso la chiamata:
```javascript
simulatoreStore.saveCalendarioToIndexedDB(toRaw(simulatoreStore.calendario));
```

Questo garantisce che le modifiche persistano anche dopo il reload della pagina.

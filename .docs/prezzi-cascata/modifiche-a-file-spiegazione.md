Va modificato il file spiegazione.md perché non corretto
 l'algoritmo da implementare deve far sì che nel momento in cui uno qualsiasi degli input box o delle Selt che si trovano nel componente del simulatore vengono modificati tutti gli elementi aprezzo apprezzo1 eccetera Devono essere ricalcolate

aprezzo_ori: finalPrice,
aprezzo: this.finalPriceN(0, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo1: this.finalPriceN(1, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo2: this.finalPriceN(2, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo3: this.finalPriceN(3, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo4: this.finalPriceN(4, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo5: this.finalPriceN(5, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo6: this.finalPriceN(6, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo7: this.finalPriceN(7, prezzoCorrenteDaModifiche, sigla.siglaUA),
aprezzo8: this.finalPriceN(8, prezzoCorrenteDaModifiche, sigla.siglaUA),

aprezzo_ori Serve a mantenere il valore senza modifiche del primo prezzo e viene utilizzato qualora tutti i valori dell'input box fossero nulli o zero, In questo caso viene passato come valore alla funzione this.finalPriceN.

Siccome si possono Inserire contemporaneamente nelle varie input box relative a un giorno valori diversi per calcolare il prezzoCorrenteDaModifiche Bisogna usare l'algoritmo che a cascata decide quali fra i vari prezzi determinati dai Vari valori inseriti debba essere utilizzato chiaramente questo algoritmo va inserito nella funzione che viene chiamata dopo la modifica in modo da dare ai vari aprezzo il valore corretto.

 cerca di chiarire il più possibile qual è il lavoro da fare e suggeriscimi il codice da inserire all'interno del file useSsimulatore.js

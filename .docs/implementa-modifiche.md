# Piano di Implementazione - Modifiche Sezione FOCUS

## Stato Attuale dell'Implementazione

### ✅ **IMPLEMENTATO**
1. **Importazione Prenotazioni da File**
   - File: `components/focus/FiltroFocus.vue` (linee 35-41)
   - Funzionalità: Upload file CSV/TXT per prenotazioni
   - Store: `usePrenotazioniDownloadStore.js` gestisce il processamento

2. **Gestione File Prezzi Concorrenza**
   - File: `components/focus/FiltroFocus.vue` (linee 25-32)
   - Label attuale: "Scarica Prezzi Concorrenza"
   - Funzionalità base implementata

3. **Composable per API Booking**
   - File: `composables/useSimpleBookingDownload.js`
   - Implementazione completa per chiamate API XML
   - Supporto per Simple Booking API

### ❌ **NON IMPLEMENTATO / DA MODIFICARE**

#### 1. <PERSON><PERSON><PERSON> "Importa Prenotazioni (API)"
**Stato**: Mancante
**Richiesto**: Aggiungere bottone separato per importazione via API

#### 2. Modifica Testo File Concorrenza
**Stato**: Testo attuale "Scarica Prezzi Concorrenza"
**Richiesto**: "Scegli file Prezzi Concorrenza ed Importali in Focus"

---

## Piano di Implementazione

### **FASE 1: Modifica Testo Esistente** ⏱️ 15 min

#### File da modificare: `components/focus/FiltroFocus.vue`

```vue
<!-- PRIMA (linea 30) -->
:label="t('Scarica Prezzi Concorrenza')"

<!-- DOPO -->
:label="t('Scegli file Prezzi Concorrenza ed Importali in Focus')"
```

#### File da modificare: `locales/it.json`
```json
{
  "Scegli file Prezzi Concorrenza ed Importali in Focus": "Scegli file Prezzi Concorrenza ed Importali in Focus"
}
```

### **FASE 2: Aggiunta Bottone API Prenotazioni** ⏱️ 45 min

#### 2.1 Modifica Layout FiltroFocus.vue

**Posizione**: Dopo il file input delle prenotazioni (linea 41)

```vue
<v-row class="mt-2">
  <v-col cols="6">
    <!-- File input esistente per prenotazioni -->
    <v-file-input
      class="mt-2"
      accept=".txt,.csv"
      v-model="filePrenotazioni"
      append-inner-icon="ri-download-2-line"
      prepend-icon=""
      :label="t('Importa Prenotazioni da PMS')"
      @change="onFilePrenotazioni"
    />
    
    <!-- NUOVO: Bottone API Prenotazioni -->
    <v-btn 
      color="primary" 
      class="mt-2" 
      @click="openApiDialog"
      :loading="loadingApi"
      block
    >
      <v-icon left>ri-cloud-line</v-icon>
      {{ t('Importa Prenotazioni (API)') }}
    </v-btn>
  </v-col>
</v-row>
```

#### 2.2 Aggiunta Dialog per Configurazione API

```vue
<!-- Dialog per configurazione API -->
<v-dialog v-model="apiDialog" max-width="600px">
  <v-card>
    <v-card-title>
      <span class="text-h5">{{ t('Configurazione API Prenotazioni') }}</span>
    </v-card-title>
    
    <v-card-text>
      <v-container>
        <v-row>
          <v-col cols="12">
            <v-select
              v-model="selectedApiType"
              :items="apiTypes"
              :label="t('Tipo API')"
              item-title="name"
              item-value="value"
            />
          </v-col>
          
          <!-- Configurazione Simple Booking -->
          <template v-if="selectedApiType === 'simple-booking'">
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="apiConfig.providerName"
                :label="t('Provider Name')"
                required
              />
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="apiConfig.providerPassword"
                :label="t('Provider Password')"
                type="password"
                required
              />
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="apiConfig.xmlHotelAgentName"
                :label="t('Hotel Agent Name')"
                required
              />
            </v-col>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="apiConfig.xmlHotelAgentPassword"
                :label="t('Hotel Agent Password')"
                type="password"
                required
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="apiConfig.hotelCode"
                :label="t('Codice Hotel')"
                required
              />
            </v-col>
            <v-col cols="12">
              <v-text-field
                v-model="apiConfig.url"
                :label="t('URL API')"
                required
              />
            </v-col>
          </template>
        </v-row>
      </v-container>
    </v-card-text>
    
    <v-card-actions>
      <v-spacer />
      <v-btn color="blue darken-1" text @click="apiDialog = false">
        {{ t('Annulla') }}
      </v-btn>
      <v-btn 
        color="blue darken-1" 
        text 
        @click="importFromApi"
        :loading="loadingApi"
        :disabled="!isApiConfigValid"
      >
        {{ t('Importa') }}
      </v-btn>
    </v-card-actions>
  </v-card>
</v-dialog>
```

#### 2.3 Logica JavaScript

```javascript
// Nuove ref
const apiDialog = ref(false);
const loadingApi = ref(false);
const selectedApiType = ref('simple-booking');
const apiConfig = ref({
  providerName: '',
  providerPassword: '',
  xmlHotelAgentName: '',
  xmlHotelAgentPassword: '',
  hotelCode: '',
  url: ''
});

// Tipi API supportati
const apiTypes = ref([
  { name: 'Simple Booking', value: 'simple-booking' },
  { name: 'Booking.com', value: 'booking-com' },
  { name: 'Expedia', value: 'expedia' }
]);

// Computed per validazione
const isApiConfigValid = computed(() => {
  if (selectedApiType.value === 'simple-booking') {
    return apiConfig.value.providerName && 
           apiConfig.value.providerPassword && 
           apiConfig.value.xmlHotelAgentName && 
           apiConfig.value.xmlHotelAgentPassword && 
           apiConfig.value.hotelCode && 
           apiConfig.value.url;
  }
  return false;
});

// Funzioni
function openApiDialog() {
  // Carica configurazione salvata se esiste
  loadApiConfig();
  apiDialog.value = true;
}

async function importFromApi() {
  loadingApi.value = true;
  
  try {
    if (selectedApiType.value === 'simple-booking') {
      await importFromSimpleBooking();
    }
    // Altri tipi API...
    
    apiDialog.value = false;
    emit('reload-calendario');
  } catch (error) {
    console.error('Errore importazione API:', error);
    // Mostra errore all'utente
  } finally {
    loadingApi.value = false;
  }
}

async function importFromSimpleBooking() {
  const { useSimpleBookingDownload } = await import('@/composables/useSimpleBookingDownload');
  
  const parametri = [
    { name: 'providerName', value: apiConfig.value.providerName },
    { name: 'providerPassword', value: apiConfig.value.providerPassword },
    { name: 'xmlHotelAgentName', value: apiConfig.value.xmlHotelAgentName },
    { name: 'xmlHotelAgentPassword', value: apiConfig.value.xmlHotelAgentPassword },
    { name: 'hotelCode', value: apiConfig.value.hotelCode },
    { name: 'url', value: apiConfig.value.url }
  ];
  
  const prenotazioni = await useSimpleBookingDownload(parametri);
  
  // Processa le prenotazioni come se fossero da file
  const result = prenotazioniDownloadStore.raggruppaDati(clienteStore.cliente, prenotazioni);
  
  // Salva configurazione per uso futuro
  saveApiConfig();
  
  return result;
}

function loadApiConfig() {
  const saved = localStorage.getItem(`api-config-${clienteStore.cliente.numero_cliente}`);
  if (saved) {
    apiConfig.value = JSON.parse(saved);
  }
}

function saveApiConfig() {
  localStorage.setItem(
    `api-config-${clienteStore.cliente.numero_cliente}`, 
    JSON.stringify(apiConfig.value)
  );
}
```

### **FASE 3: Aggiornamento Traduzioni** ⏱️ 10 min

#### File: `locales/it.json`
```json
{
  "Importa Prenotazioni (API)": "Importa Prenotazioni (API)",
  "Configurazione API Prenotazioni": "Configurazione API Prenotazioni",
  "Tipo API": "Tipo API",
  "Provider Name": "Nome Provider",
  "Provider Password": "Password Provider",
  "Hotel Agent Name": "Nome Agente Hotel",
  "Hotel Agent Password": "Password Agente Hotel",
  "Codice Hotel": "Codice Hotel",
  "URL API": "URL API",
  "Annulla": "Annulla",
  "Importa": "Importa"
}
```

### **FASE 4: Testing e Validazione** ⏱️ 30 min

#### 4.1 Test Funzionalità
- [ ] Verifica modifica testo file concorrenza
- [ ] Test apertura dialog API
- [ ] Test validazione campi obbligatori
- [ ] Test importazione Simple Booking
- [ ] Test salvataggio/caricamento configurazione

#### 4.2 Test UI/UX
- [ ] Verifica responsive design
- [ ] Test stati di loading
- [ ] Test gestione errori
- [ ] Verifica accessibilità

---

## Stima Tempi Totali

| Fase | Tempo Stimato | Complessità |
|------|---------------|-------------|
| Modifica testo | 15 min | Bassa |
| Bottone API | 45 min | Media |
| Traduzioni | 10 min | Bassa |
| Testing | 30 min | Media |
| **TOTALE** | **1h 40min** | **Media** |

---

## Note Implementative

### Considerazioni Tecniche
1. **Riutilizzo Codice**: Il composable `useSimpleBookingDownload` è già implementato
2. **Store Integration**: Utilizzare `usePrenotazioniDownloadStore` esistente
3. **Persistenza**: Salvare configurazioni API in localStorage per cliente
4. **Error Handling**: Implementare gestione errori robusta per chiamate API

### Estensibilità Futura
1. **Nuovi Provider**: Struttura modulare per aggiungere altri provider API
2. **Configurazioni Avanzate**: Possibilità di aggiungere filtri temporali
3. **Scheduling**: Potenziale per importazioni automatiche programmate

### Sicurezza
1. **Credenziali**: Non salvare password in localStorage (solo configurazioni base)
2. **Validazione**: Validare tutti gli input prima delle chiamate API
3. **HTTPS**: Assicurarsi che tutte le chiamate API usino HTTPS

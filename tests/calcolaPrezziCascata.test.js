// Test per la funzione calcolaPrezziCascata
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useVariazionePrezziOccupazioneStore } from '@/stores/useVariazionePrezziOccupazioneStore';
import { useSimulatore } from '@/stores/useSimulatore';

// Mock dello store useVariazionePrezziOccupazioneStore
vi.mock('@/stores/useVariazionePrezziOccupazioneStore', () => ({
  useVariazionePrezziOccupazioneStore: vi.fn(() => ({
    getVariazioneByOccupancy: (occupancy) => {
      // Mock della risposta in base all'occupancy
      if (occupancy <= 30) return 1.2; // Bassa occupazione, prezzo più alto
      if (occupancy <= 60) return 1.1; // Media occupazione
      return 1.0;                       // Alta occupazione, prezzo standard
    },
    mappaOccupazione: {}
  }))
}));

describe('calcolaPrezziCascata', () => {
  let simulatoreStore;
  
  beforeEach(() => {
    setActivePinia(createPinia());
    simulatoreStore = useSimulatore();
  });

  // Esponiamo la funzione calcolaPrezziCascata per i test
  // Questa è una versione semplificata senza dipendenze esterne
  const calcolaPrezziCascata = ({
    costo = 100,
    molReale = 0.3,
    molForzato = 0.25,
    disoccupazione = 0.5,
    tipoTariffa = 'R',
    modificaPerc = 0,
    modificaPercSigla = 0,
    garantito = 0,
    forzato = 0
  }) => {
    let prezzo1 = 0;
    
    // Step 1: Calcola il prezzo base secondo il tipo tariffa
    if (tipoTariffa === 'C') {
      prezzo1 = costo;
    } else if (tipoTariffa === 'M') {
      const molDaUsare = (molReale > molForzato) ? molReale : molForzato;
      prezzo1 = costo * molDaUsare + costo;
    } else { // Default 'R' - Revenue
      const molDaUsare = (molReale > molForzato) ? molReale : molForzato;
      const mup = costo * molDaUsare + costo;
      
      const variazionePrezziOccupazioneStore = useVariazionePrezziOccupazioneStore();
      const br = variazionePrezziOccupazioneStore.getVariazioneByOccupancy(disoccupazione * 100);
      prezzo1 = mup * br;
    }
    
    // Step 2: Piano tariffario (non implementato)
    const prezzo2 = prezzo1;
    
    // Step 3: Applica modifica percentuale colonna
    const prezzo3 = modificaPerc !== 0 ? prezzo2 * (1 + modificaPerc / 100) : prezzo2;
    
    // Step 4: Applica modifica percentuale sigla
    const prezzo4 = modificaPercSigla !== 0 ? prezzo3 * (1 + modificaPercSigla / 100) : prezzo3;
    
    // Step 5: Applica prezzo garantito
    const prezzo5 = garantito > 0 && prezzo4 < garantito ? garantito : prezzo4;
    
    // Step 6: Applica prezzo forzato (override totale)
    const prezzo6 = forzato > 0 ? forzato : prezzo5;
    
    return {
      prezzo1: Math.floor(prezzo1),
      prezzo2: Math.floor(prezzo2),
      prezzo3: Math.floor(prezzo3),
      prezzo4: Math.floor(prezzo4),
      prezzo5: Math.floor(prezzo5),
      prezzo6: Math.floor(prezzo6),
      profitto: Math.floor(prezzo6 - costo),
      prezzoFinale: Math.floor(prezzo6)
    };
  };

  it('Calcola correttamente il prezzo base (Prezzo1) in base al tipo tariffa C, M, R', () => {
    // Test tipo C (costo)
    const risultatoC = calcolaPrezziCascata({
      costo: 100,
      tipoTariffa: 'C'
    });
    expect(risultatoC.prezzo1).toBe(100);
    
    // Test tipo M (MUP)
    const risultatoM = calcolaPrezziCascata({
      costo: 100,
      molReale: 0.3,
      molForzato: 0.25,
      tipoTariffa: 'M'
    });
    expect(risultatoM.prezzo1).toBe(130); // 100 + (100 * 0.3)
    
    // Test tipo R (Revenue)
    const risultatoR = calcolaPrezziCascata({
      costo: 100,
      molReale: 0.3,
      molForzato: 0.25,
      disoccupazione: 0.5, // 50% di occupazione
      tipoTariffa: 'R'
    });
    // MUP = 130, con occupazione del 50% (coefficiente 1.1)
    expect(risultatoR.prezzo1).toBe(143); // 130 * 1.1
  });

  it('Applica correttamente modifiche percentuali di colonna e sigla', () => {
    const risultato = calcolaPrezziCascata({
      costo: 100,
      tipoTariffa: 'C',
      modificaPerc: 10,      // +10%
      modificaPercSigla: 5   // +5%
    });
    
    expect(risultato.prezzo1).toBe(100);
    expect(risultato.prezzo3).toBe(110); // 100 * 1.1
    expect(risultato.prezzo4).toBe(115); // 110 * 1.05
  });

  it('Applica correttamente prezzi garantiti e forzati', () => {
    // Test prezzo garantito attivato
    const risultatoGarantito = calcolaPrezziCascata({
      costo: 100,
      tipoTariffa: 'C',
      garantito: 150
    });
    expect(risultatoGarantito.prezzo5).toBe(150);
    
    // Test prezzo garantito non attivato (prezzo già superiore)
    const risultatoGarantitoInattivo = calcolaPrezziCascata({
      costo: 200,
      tipoTariffa: 'C',
      garantito: 150
    });
    expect(risultatoGarantitoInattivo.prezzo5).toBe(200);
    
    // Test prezzo forzato
    const risultatoForzato = calcolaPrezziCascata({
      costo: 100,
      tipoTariffa: 'C',
      garantito: 150,
      forzato: 180
    });
    expect(risultatoForzato.prezzo6).toBe(180);
  });

  it('Calcola correttamente profitto e prezzo finale', () => {
    const risultato = calcolaPrezziCascata({
      costo: 100,
      tipoTariffa: 'R',
      disoccupazione: 0.2,  // 20% occupazione
      modificaPerc: 5,
      modificaPercSigla: 3,
      garantito: 120,
      forzato: 0
    });
    
    // Verifico che il profitto sia correttamente calcolato
    expect(risultato.profitto).toBe(risultato.prezzo6 - 100);
    
    // Verifico che il prezzo finale sia uguale a prezzo6
    expect(risultato.prezzoFinale).toBe(risultato.prezzo6);
  });
});

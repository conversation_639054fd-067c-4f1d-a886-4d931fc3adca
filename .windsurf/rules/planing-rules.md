---
trigger: always_on
---

- quando pianifichi segnala la mancanza di corrispondenza o le cose non chiare con il simbolo (???)
- ogni task del piano deve avere all'inizio [ ] che servirà per indicare se il task è da svolgere [ ] o è stato svolto [X] inoltre ogni task deve essere numerato con un progressivo
- i sotto task devono avere la numerazione <numerotask>.<progressivo>
- finito un task aggiornalo come fatto con [x]
